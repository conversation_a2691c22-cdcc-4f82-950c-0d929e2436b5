"""
BGE-M3嵌入服务核心实现
基于现有的BGE嵌入服务进行优化，专门用于独立的嵌入服务
"""

import gc
import torch
import numpy as np
from typing import List, Dict, Any
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from FlagEmbedding import BGEM3FlagModel
from sklearn.preprocessing import normalize

from shared.utils.logger import get_logger
from shared.system_compatibility import get_system_config
from services.embedding_service.config import config

logger = get_logger(__name__)


def get_optimal_device(device: str = "auto") -> str:
    """获取最优设备"""
    if device == "auto":
        if torch.cuda.is_available():
            return "cuda"
        else:
            return "cpu"
    return device


def recommend_device_settings() -> Dict[str, Any]:
    """推荐设备设置"""
    settings = {"use_fp16": torch.cuda.is_available(), "batch_size": 32, "warnings": []}

    if torch.cuda.is_available():
        # GPU可用时的推荐设置
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        if gpu_memory < 8:
            settings["batch_size"] = 16
            settings["warnings"].append("GPU内存较小，建议使用较小的batch_size")
    else:
        settings["use_fp16"] = False
        settings["batch_size"] = 16
        settings["warnings"].append("未检测到GPU，使用CPU模式")

    return settings


class BGEEmbeddingService:
    """BGE-M3嵌入服务 - 专门用于独立嵌入服务的优化实现"""

    def __init__(
        self,
        model_name: str = None,
        device: str = "auto",
        batch_size: int = 32,
        max_length: int = 8192,
        normalize_embeddings: bool = True,
        enable_cache: bool = False,  # 由外部缓存管理器处理
        auto_cleanup: bool = True,
        memory_monitor: bool = True,
    ):
        """
        初始化BGE-M3嵌入服务

        Args:
            model_name: 模型名称或路径
            device: 设备 (cpu, cuda, auto)
            batch_size: 批处理大小
            max_length: 最大序列长度
            normalize_embeddings: 是否标准化嵌入向量
            enable_cache: 是否启用内部缓存（通常由外部管理）
            auto_cleanup: 是否启用自动内存清理
            memory_monitor: 是否启用内存监控
        """
        # 基础配置
        self.model_name = model_name or config.model_name
        self.device = self._determine_device(device)
        self.batch_size = batch_size
        self.max_length = max_length
        self.normalize_embeddings = normalize_embeddings
        self.enable_cache = enable_cache
        self.auto_cleanup = auto_cleanup
        self.memory_monitor = memory_monitor

        # 设备相关配置
        self.use_fp16 = self.device == "cuda"

        # 内存管理配置
        self.cleanup_threshold = getattr(config, "memory_cleanup_threshold", 0.8)
        self.batch_cleanup_interval = 5  # 每5个批次清理一次

        # 模型相关
        self.model = None

        # 性能统计
        self.stats = {
            "total_embeds": 0,
            "total_texts": 0,
            "total_time": 0.0,
            "avg_time_per_text": 0.0,
        }

        logger.info(
            f"BGE嵌入服务初始化: 设备={self.device}, 批次大小={self.batch_size}"
        )

        if self.memory_monitor:
            self.log_memory_usage("初始化完成")

    def _determine_device(self, device: str) -> str:
        """确定使用的设备 - 使用系统兼容性检测"""
        system_config = get_system_config()

        # 如果系统要求强制使用CPU
        if system_config.force_cpu:
            logger.info(f"系统兼容性检测: {system_config.system_name} 强制使用CPU模式")
            return "cpu"

        # 使用系统推荐的设备
        if device == "auto":
            recommended_device = system_config.device
            logger.info(f"系统兼容性检测: 推荐设备 {recommended_device}")
            return get_optimal_device(device)

        return device

    def _load_model(self):
        """加载BGE-M3嵌入模型 - 仅从本地路径加载"""
        try:
            logger.info(f"正在加载 BGE-M3 嵌入模型")
            logger.info(f"本地模型路径: {self.model_name}")
            logger.info(f"设备: {self.device}")

            # 验证本地模型路径存在
            model_path = Path(self.model_name)
            if not model_path.exists():
                raise FileNotFoundError(f"本地模型路径不存在: {model_path}")

            # 检查必要的模型文件
            required_files = ["config.json", "model.safetensors", "tokenizer.json"]
            missing_files = []
            for file_name in required_files:
                if not (model_path / file_name).exists():
                    missing_files.append(file_name)

            if missing_files:
                raise FileNotFoundError(f"缺少必要的模型文件: {missing_files}")

            logger.info(f"✅ 本地模型文件验证通过")

            # 加载BGE-M3模型
            self.model = BGEM3FlagModel(
                str(model_path), use_fp16=self.use_fp16, device=self.device
            )

            logger.info(f"✅ BGE-M3 模型加载成功")
            logger.info(f"   模型设备: {self.device}")
            logger.info(f"   使用FP16: {self.use_fp16}")

        except Exception as e:
            logger.error(f"❌ BGE-M3 模型加载失败: {e}")

            # 模型加载失败时清理可能的部分加载状态
            if hasattr(self, "model"):
                self.model = None

            # 清理可能占用的显存
            gc.collect()
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()

            # 提供详细的错误信息
            error_msg = f"无法从本地路径加载 BGE-M3 嵌入模型: {e}\n\n"
            error_msg += "故障排除建议:\n"
            error_msg += "1. 确保已安装 FlagEmbedding 库: pip install FlagEmbedding\n"
            error_msg += "2. 检查本地模型路径是否正确\n"
            error_msg += "3. 验证模型文件完整性（config.json, model.safetensors, tokenizer.json）\n"
            error_msg += "4. 检查磁盘空间，BGE-M3 需要约 2.3GB 空间\n"
            error_msg += f"5. 当前模型路径: {self.model_name}\n"

            if "CUDA" in str(e) or "cuda" in str(e):
                error_msg += (
                    "6. CUDA 相关错误：尝试设置 device='cpu' 或检查 CUDA 安装\n"
                )

            raise RuntimeError(error_msg)

    def log_memory_usage(self, stage: str = ""):
        """记录内存使用情况"""
        try:
            if torch.cuda.is_available() and self.device == "cuda":
                allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                reserved = torch.cuda.memory_reserved() / 1024**3  # GB
                logger.debug(
                    f"🔍 GPU内存使用 [{stage}]: 已分配={allocated:.2f}GB, 已保留={reserved:.2f}GB"
                )
            else:
                import psutil

                memory_info = psutil.virtual_memory()
                used_gb = memory_info.used / 1024**3
                total_gb = memory_info.total / 1024**3
                logger.debug(
                    f"🔍 系统内存使用 [{stage}]: {used_gb:.2f}GB / {total_gb:.2f}GB ({memory_info.percent:.1f}%)"
                )

        except Exception as e:
            logger.warning(f"内存监控时出现警告: {e}")

    def cleanup_memory(self, force: bool = False):
        """清理内存 - 增强GPU显存清理，保持模型加载"""
        try:
            if self.memory_monitor:
                self.log_memory_usage("清理前")

            # ★★★ 增强：先同步所有GPU操作 ★★★
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.synchronize()

            # 强制垃圾回收
            gc.collect()

            # ★★★ 增强：GPU内存清理 - 更彻底的清理策略 ★★★
            if torch.cuda.is_available() and self.device == "cuda":
                # 清空CUDA缓存
                torch.cuda.empty_cache()
                # 重置峰值内存统计
                torch.cuda.reset_max_memory_allocated()
                # 再次同步确保清理完成
                torch.cuda.synchronize()
                logger.debug("✅ GPU显存缓存已清理并同步")

            # 根据系统配置决定是否清理模型引用
            system_config = get_system_config()
            should_cleanup_model = force

            # 清理模型引用（仅在强制要求时）
            if should_cleanup_model and self.model is not None:
                logger.info("🔄 清理模型引用...")
                del self.model
                self.model = None
                gc.collect()
                if torch.cuda.is_available() and self.device == "cuda":
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                logger.info("✅ 模型引用已清理")

            # ★★★ 增强：延迟确保清理生效 ★★★
            import time
            time.sleep(0.1)  # 100ms延迟确保GPU操作完成

            if self.memory_monitor:
                self.log_memory_usage("清理后")

        except Exception as e:
            logger.warning(f"内存清理时出现警告: {e}")

    def cleanup_gpu_memory_only(self):
        """
        仅清理GPU显存，不清理模型引用
        专门用于向量化处理后的显存清理
        """
        try:
            if torch.cuda.is_available() and self.device == "cuda":
                if self.memory_monitor:
                    self.log_memory_usage("GPU显存清理前")

                # 同步所有GPU操作
                torch.cuda.synchronize()

                # 强制垃圾回收
                gc.collect()

                # 清空CUDA缓存
                torch.cuda.empty_cache()

                # 重置峰值内存统计
                torch.cuda.reset_max_memory_allocated()

                # 再次同步确保清理完成
                torch.cuda.synchronize()

                # 短暂延迟确保清理生效
                import time
                time.sleep(0.05)  # 50ms延迟

                if self.memory_monitor:
                    self.log_memory_usage("GPU显存清理后")

                logger.debug("✅ GPU显存专项清理完成")
            else:
                # CPU模式下的内存清理
                gc.collect()
                logger.debug("💻 CPU模式内存清理完成")

        except Exception as e:
            logger.warning(f"GPU显存专项清理时出现警告: {e}")

    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            memory_info = {}

            if torch.cuda.is_available() and self.device == "cuda":
                allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                reserved = torch.cuda.memory_reserved() / 1024**3  # GB
                memory_info.update(
                    {
                        "gpu_allocated_gb": round(allocated, 2),
                        "gpu_reserved_gb": round(reserved, 2),
                        "device": "cuda",
                    }
                )
            else:
                import psutil

                vm = psutil.virtual_memory()
                memory_info.update(
                    {
                        "system_used_gb": round(vm.used / 1024**3, 2),
                        "system_total_gb": round(vm.total / 1024**3, 2),
                        "system_percent": round(vm.percent, 1),
                        "device": "cpu",
                    }
                )

            return memory_info

        except Exception as e:
            logger.warning(f"获取内存使用情况时出现警告: {e}")
            return {"error": str(e)}

    def reload_model(self):
        """重新加载模型"""
        logger.info("🔄 重新加载BGE-M3模型...")

        # 先清理现有模型
        if self.model is not None:
            del self.model
            self.model = None
            self.cleanup_memory()

        # 重新加载模型
        self._load_model()

        if self.memory_monitor:
            self.log_memory_usage("模型重载完成")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        嵌入文档列表 - 基于现有实现优化

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        if not texts:
            return []

        if self.model is None:
            raise RuntimeError("模型未加载")

        if self.memory_monitor:
            self.log_memory_usage("开始嵌入")

        import time

        start_time = time.time()

        try:
            embeddings = []
            batch_count = 0

            # 批处理嵌入
            for i in range(0, len(texts), self.batch_size):
                batch_texts = texts[i : i + self.batch_size]
                batch_count += 1

                try:
                    # 生成嵌入向量
                    batch_output = self.model.encode(
                        batch_texts,
                        batch_size=len(batch_texts),
                        max_length=self.max_length,
                        return_dense=True,
                        return_sparse=False,
                        return_colbert_vecs=False,
                    )

                    # 提取密集嵌入向量
                    batch_embeddings = batch_output["dense_vecs"]

                    # ★★★ 增强：立即转换为numpy数组并释放GPU张量 ★★★
                    if hasattr(batch_embeddings, "cpu"):
                        batch_embeddings = batch_embeddings.cpu().numpy()
                    elif hasattr(batch_embeddings, "detach"):
                        batch_embeddings = batch_embeddings.detach().cpu().numpy()

                    # 确保是numpy数组格式
                    batch_embeddings = np.array(batch_embeddings, dtype=np.float32)

                    # 标准化（如果启用）
                    if self.normalize_embeddings:
                        batch_embeddings = normalize(
                            batch_embeddings, norm="l2", axis=1
                        )

                    # 转换为列表格式
                    embeddings.extend(batch_embeddings.tolist())

                    # ★★★ 增强：每个批次后立即清理GPU显存 ★★★
                    if self.auto_cleanup and torch.cuda.is_available() and self.device == "cuda":
                        # 立即清理当前批次的GPU显存
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()

                    logger.debug(
                        f"批次 {batch_count} 处理完成: {len(batch_texts)} 个文本"
                    )

                    # ★★★ 修改：更频繁的显存清理（每2个批次而不是5个） ★★★
                    if (
                        self.auto_cleanup
                        and batch_count % 2 == 0  # 每2个批次清理一次
                    ):
                        self.cleanup_gpu_memory_only()

                except Exception as e:
                    logger.error(f"批次 {batch_count} 处理失败: {e}")
                    raise

            # 更新统计信息
            processing_time = time.time() - start_time
            self.stats["total_embeds"] += 1
            self.stats["total_texts"] += len(texts)
            self.stats["total_time"] += processing_time
            self.stats["avg_time_per_text"] = (
                self.stats["total_time"] / self.stats["total_texts"]
            )

            logger.debug(
                f"嵌入完成: {len(texts)} 个文档，{len(embeddings)} 个向量，耗时 {processing_time:.3f}秒"
            )

            # ★★★ 新增：每次嵌入完成后自动清理GPU显存 ★★★
            if self.auto_cleanup:
                self.cleanup_gpu_memory_only()

            if self.memory_monitor:
                self.log_memory_usage("嵌入完成")

            return embeddings

        except Exception as e:
            logger.error(f"文档嵌入失败: {e}")
            # 发生错误时进行内存清理
            if self.auto_cleanup:
                self.cleanup_gpu_memory_only()
            raise

    def embed_query(self, text: str) -> List[float]:
        """
        嵌入查询文本

        Args:
            text: 查询文本

        Returns:
            嵌入向量
        """
        embeddings = self.embed_documents([text])
        return embeddings[0] if embeddings else []

    def get_embedding_dimension(self) -> int:
        """
        获取嵌入维度

        Returns:
            嵌入维度
        """
        return 1024  # BGE-M3固定维度

    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self.stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_embeds": 0,
            "total_texts": 0,
            "total_time": 0.0,
            "avg_time_per_text": 0.0,
        }
